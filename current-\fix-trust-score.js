// Quick script to fix trust score for test2 user

async function fixTrustScore() {
  try {
    // First, let's check the current trust score
    console.log('🔍 Checking current trust score for test2...');

    // Note: This would need proper authentication in a real scenario
    // For now, we'll just document the issue and solution

    console.log(`
📋 TRUST SCORE ISSUE IDENTIFIED:
- User: test2 (ID: 2)
- Current Trust Score: 30 (base score only)
- Identity Verified: ✅ true
- Identity Status: ✅ approved
- Expected Trust Score: 70 (30 base + 40 identity bonus)
- Missing Points: 40

🔧 SOLUTION:
User test2 needs to call: POST /api/user/trust-score/fix
This will automatically add the missing 40 points for identity verification.

📝 STEPS TO FIX:
1. Login as test2
2. Call POST /api/user/trust-score/fix
3. Trust score will be updated from 30 → 70
4. User will be able to list in SafeSphere (requires 70+ trust score)
`);

  } catch (error) {
    console.error('Error:', error);
  }
}

fixTrustScore();
