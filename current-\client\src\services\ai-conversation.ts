interface AIResponse {
  intent: 'search' | 'navigation' | 'autoshop' | 'conversation' | 'unknown';
  action?: string;
  parameters?: Record<string, any>;
  response: string;
  confidence: number;
}

interface ConversationContext {
  userId?: string;
  sessionId: string;
  history: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
}

class AIConversationService {
  private context: ConversationContext;

  constructor() {
    this.context = {
      sessionId: this.generateSessionId(),
      history: []
    };
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  async processCommand(userInput: string): Promise<AIResponse> {
    try {
      // Add user message to history
      this.context.history.push({
        role: 'user',
        content: userInput,
        timestamp: Date.now()
      });

      // Try OpenAI API first
      try {
        const response = await this.processWithOpenAI(userInput);

        // Add AI response to history
        this.context.history.push({
          role: 'assistant',
          content: response.response,
          timestamp: Date.now()
        });

        return response;
      } catch (apiError) {
        console.warn('OpenAI API failed, falling back to patterns:', apiError);

        // Fallback to pattern-based processing
        const response = this.processWithPatterns(userInput);

        // Add AI response to history
        this.context.history.push({
          role: 'assistant',
          content: response.response,
          timestamp: Date.now()
        });

        return response;
      }
    } catch (error) {
      console.error('AI conversation error:', error);
      return {
        intent: 'unknown',
        response: 'I apologize, but I encountered an error processing your request. Please try again.',
        confidence: 0.1
      };
    }
  }

  private async processWithOpenAI(userInput: string): Promise<AIResponse> {
    const response = await fetch('/api/ai-conversation/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        message: userInput,
        conversationHistory: this.context.history.slice(-5).map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error('API returned error');
    }

    return data.response;
  }

  private processWithPatterns(input: string): AIResponse {
    const lowerInput = input.toLowerCase().trim();

    // Navigation patterns (following DasWos Coins successful pattern)
    const navigationPatterns = [
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:daswos\s+)?coins/i, route: '/daswos-coins', name: 'DasWos Coins' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:shopping\s+)?cart/i, route: '/cart', name: 'cart' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:auto\s*shop|autoshop)/i, route: '/autoshop-dashboard', name: 'AutoShop dashboard' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?profile/i, route: '/profile', name: 'profile' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?orders/i, route: '/orders', name: 'orders' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?purchases/i, route: '/purchases', name: 'purchases' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?listings/i, route: '/my-listings', name: 'listings' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:user\s+)?settings/i, route: '/user-settings', name: 'settings' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?(?:sign in|login|auth|authentication)(?:\s+page)?/i, route: '/auth', name: 'sign in portal' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?home(?:\s+page)?/i, route: '/', name: 'home' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?search(?:\s+page)?/i, route: '/search', name: 'search' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?sell(?:\s+page)?/i, route: '/sell', name: 'sell page' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:split\s+)?buy/i, route: '/split-buy', name: 'SplitBuy' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:das\s+)?list/i, route: '/d-list', name: 'das.list' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:browse\s+)?jobs/i, route: '/browse-jobs', name: 'jobs' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:ai\s+)?assistant/i, route: '/ai-assistant', name: 'AI assistant' },
      { pattern: /(?:show|display)\s+(?:my\s+)?profile/i, route: '/profile', name: 'profile' },
      { pattern: /(?:show|display)\s+(?:my\s+)?cart/i, route: '/cart', name: 'cart' },
      { pattern: /(?:go\s+)?home/i, route: '/', name: 'home' },
      { pattern: /(?:sign in|log in|login|authenticate)/i, route: '/auth', name: 'sign in portal' },
    ];

    for (const { pattern, route, name } of navigationPatterns) {
      if (pattern.test(lowerInput)) {
        return {
          intent: 'navigation',
          action: 'navigate',
          parameters: { route },
          response: `Navigating to your ${name}...`,
          confidence: 0.9
        };
      }
    }

    // AutoShop patterns
    const autoShopPatterns = [
      /(?:start|enable|activate|begin|turn on)\s+(?:auto\s*shop|autoshop)/i,
      /(?:let's|lets)\s+(?:auto\s*shop|autoshop)/i,
      /(?:auto\s*shop|autoshop)\s+(?:start|begin|go|now)/i,
      /(?:start|begin)\s+(?:automatic\s+)?shopping/i,
      /(?:shop for me|shop automatically)/i,
      /(?:enable|activate)\s+(?:automatic\s+)?shopping/i,
    ];

    for (const pattern of autoShopPatterns) {
      if (pattern.test(lowerInput)) {
        return {
          intent: 'autoshop',
          action: 'start_autoshop',
          parameters: {},
          response: 'Starting AutoShop for you. I\'ll help you find and purchase items automatically!',
          confidence: 0.9
        };
      }
    }

    // Search patterns
    const searchPatterns = [
      /(?:search for|find|look for|show me|find me)\s+(.+)/i,
      /(?:search)\s+(.+)/i,
      /(?:i want to buy|i need|get me|buy me|purchase)\s+(.+)/i,
      /(?:can you find|help me find|locate)\s+(.+)/i,
      /(?:what about|how about|show me some)\s+(.+)/i,
      /(?:i'm looking for|looking for)\s+(.+)/i,
    ];

    for (const pattern of searchPatterns) {
      const match = lowerInput.match(pattern);
      if (match && match[1]) {
        const searchQuery = match[1].trim().replace(/\s+(please|now|today)$/i, '');
        return {
          intent: 'search',
          action: 'search',
          parameters: { query: searchQuery },
          response: `Searching for ${searchQuery}...`,
          confidence: 0.8
        };
      }
    }

    // Greeting patterns
    if (/^(hi|hello|hey|good morning|good afternoon|good evening)$/i.test(lowerInput)) {
      return {
        intent: 'conversation',
        response: 'Hello! I\'m DasWos AI, your intelligent shopping assistant. You can ask me to search for products, navigate the site, or start AutoShop. How can I help you today?',
        confidence: 0.9
      };
    }

    // Help patterns
    if (/^(help|what can you do|commands)$/i.test(lowerInput)) {
      return {
        intent: 'conversation',
        response: 'I can help you with:\n• Search for products: "Find me shoes"\n• Navigate: "Go to my profile", "Sign in"\n• Start AutoShop: "Let\'s AutoShop"\n• General questions about DasWos\n\nWhat would you like to do?',
        confidence: 0.9
      };
    }

    // Default response for unclear input
    return {
      intent: 'conversation',
      response: 'I\'m not sure I understood that. You can ask me to search for products, navigate to different pages, or start AutoShop. Try saying something like "search for shoes" or "go to my profile".',
      confidence: 0.3
    };
  }

  getConversationHistory() {
    return this.context.history;
  }

  clearHistory() {
    this.context.history = [];
    this.context.sessionId = this.generateSessionId();
  }
}

export const aiConversationService = new AIConversationService();
export type { AIResponse, ConversationContext };
