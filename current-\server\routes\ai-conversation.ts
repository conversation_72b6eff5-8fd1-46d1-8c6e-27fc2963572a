import express from 'express';
import OpenA<PERSON> from 'openai';
import multer from 'multer';
import fs from 'fs';
import path from 'path';

const router = express.Router();

// Configure multer for audio file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept audio files and some video formats that might contain audio
    const allowedTypes = [
      'audio/webm',
      'audio/mp4',
      'audio/wav',
      'audio/mpeg',
      'audio/mp3',
      'audio/ogg',
      'audio/flac',
      'video/webm', // Sometimes MediaRecorder creates video/webm with audio
      'video/mp4'   // Sometimes MediaRecorder creates video/mp4 with audio
    ];

    console.log('File upload - MIME type:', file.mimetype, 'Original name:', file.originalname);

    if (file.mimetype.startsWith('audio/') || allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      console.log('Rejected file type:', file.mimetype);
      cb(new Error(`File type not allowed: ${file.mimetype}. Allowed types: ${allowedTypes.join(', ')}`));
    }
  }
});

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: '********************************************************************************************************************************************************************'
});

// Test endpoint to verify OpenAI connection
router.get('/test', async (req, res) => {
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Say "Hello from DasWos AI!"' }],
      max_tokens: 50
    });

    res.json({
      success: true,
      message: 'OpenAI connection successful',
      response: completion.choices[0]?.message?.content
    });
  } catch (error) {
    console.error('OpenAI test error:', error);
    res.status(500).json({
      success: false,
      error: 'OpenAI connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

interface AIResponse {
  intent: 'search' | 'navigation' | 'autoshop' | 'conversation' | 'unknown';
  action?: string;
  parameters?: Record<string, any>;
  response: string;
  confidence: number;
}

// System prompt for DasWos AI
const SYSTEM_PROMPT = `You are DasWos AI, an intelligent shopping and navigation assistant for the DasWos platform. You help users with:

1. SEARCH COMMANDS: When users want to find products
   - Examples: "search for shoes", "find me a laptop", "I need headphones", "show me gaming chairs"
   - Extract the search query and respond with search intent

2. NAVIGATION COMMANDS: When users want to go to different pages
   - Examples: "go to my profile", "show my cart", "take me to orders", "open my dashboard", "sign in"
   - Available pages: profile (/profile), cart (/cart), orders (/orders), autoshop-dashboard (/autoshop-dashboard), daswos-coins (/daswos-coins), home (/), settings (/settings), sign in portal (/auth)

3. AUTOSHOP COMMANDS: When users want to start automatic shopping
   - Examples: "start autoshop", "let's autoshop", "shop for me automatically", "enable automatic shopping"

4. CONVERSATION: For greetings, help requests, or general questions about DasWos

You must respond with a JSON object in this exact format:
{
  "intent": "search|navigation|autoshop|conversation",
  "action": "search|navigate|start_autoshop|chat",
  "parameters": {"query": "search term"} or {"route": "/page-route"} or {},
  "response": "Natural language response to the user",
  "confidence": 0.1-1.0
}

Be helpful, concise, and maintain a futuristic AI assistant personality. Always respond with valid JSON only.`;

// POST /api/ai-conversation/voice - Handle audio input with Whisper
router.post('/voice', upload.single('audio'), async (req, res) => {
  try {
    const audioFile = req.file;
    const conversationHistory = JSON.parse(req.body.conversationHistory || '[]');

    console.log('Voice input received:', {
      hasAudio: !!audioFile,
      historyLength: conversationHistory.length
    });

    if (!audioFile) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    // Transcribe audio using Whisper
    console.log('Transcribing audio with Whisper...');
    console.log('Audio file details:', {
      originalname: audioFile.originalname,
      mimetype: audioFile.mimetype,
      size: audioFile.size,
      path: audioFile.path
    });

    const audioFilePath = audioFile.path;

    const transcription = await openai.audio.transcriptions.create({
      file: fs.createReadStream(audioFilePath),
      model: 'whisper-1',
      language: 'en',
    });

    const userQuery = transcription.text;
    console.log('Whisper transcription:', userQuery);

    // Clean up the temporary file
    fs.unlinkSync(audioFilePath);

    if (!userQuery || userQuery.trim().length === 0) {
      return res.status(400).json({ error: 'No speech detected in audio' });
    }

    // Process with OpenAI
    const messages = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...conversationHistory.slice(-5),
      { role: 'user', content: userQuery }
    ];

    console.log('Processing with OpenAI...');
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages as any,
      max_tokens: 200,
      temperature: 0.7,
      response_format: { type: 'json_object' }
    });

    const aiResponseText = completion.choices[0]?.message?.content;

    if (!aiResponseText) {
      throw new Error('No response from OpenAI');
    }

    let aiResponse: AIResponse;
    try {
      aiResponse = JSON.parse(aiResponseText);
      console.log('Parsed AI response:', aiResponse);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', aiResponseText);
      aiResponse = {
        intent: 'conversation',
        response: 'I apologize, but I had trouble understanding that. Could you please rephrase your request?',
        confidence: 0.3
      };
    }

    // Generate speech response
    let audioBuffer = null;
    if (aiResponse.response) {
      try {
        console.log('Generating speech response...');
        const speech = await openai.audio.speech.create({
          model: 'tts-1',
          voice: 'nova',
          input: aiResponse.response,
        });

        audioBuffer = Buffer.from(await speech.arrayBuffer());
        console.log('Speech generated successfully');
      } catch (speechError) {
        console.error('Failed to generate speech:', speechError);
      }
    }

    res.json({
      userQuery,
      aiResponse,
      audio: audioBuffer ? audioBuffer.toString('base64') : null,
      conversationHistory: [...messages, { role: 'assistant', content: aiResponseText }]
    });

  } catch (error) {
    console.error('Voice processing error:', error);

    // Clean up file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Failed to process voice input',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/ai-conversation/process
router.post('/process', async (req, res) => {
  try {
    const { message, conversationHistory = [] } = req.body;
    console.log('AI Conversation request:', { message, historyLength: conversationHistory.length });

    if (!message || typeof message !== 'string') {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Prepare conversation messages for OpenAI
    const messages = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...conversationHistory.slice(-5), // Keep last 5 messages for context
      { role: 'user', content: message }
    ];

    console.log('Calling OpenAI API with messages:', messages.length);

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages as any,
      max_tokens: 200,
      temperature: 0.7,
      response_format: { type: 'json_object' }
    });

    const aiResponseText = completion.choices[0]?.message?.content;
    console.log('OpenAI raw response:', aiResponseText);

    if (!aiResponseText) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    let aiResponse: AIResponse;
    try {
      aiResponse = JSON.parse(aiResponseText);
      console.log('Parsed AI response:', aiResponse);
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', aiResponseText);
      // Fallback response
      aiResponse = {
        intent: 'conversation',
        response: 'I apologize, but I had trouble understanding that. Could you please rephrase your request?',
        confidence: 0.3
      };
    }

    // Validate and sanitize the response
    if (!aiResponse.intent || !aiResponse.response) {
      aiResponse = {
        intent: 'conversation',
        response: 'I\'m here to help you search for products, navigate the site, or start AutoShop. What would you like to do?',
        confidence: 0.5
      };
    }

    // Ensure confidence is within valid range
    if (typeof aiResponse.confidence !== 'number' || aiResponse.confidence < 0 || aiResponse.confidence > 1) {
      aiResponse.confidence = 0.7;
    }

    res.json({
      success: true,
      response: aiResponse,
      usage: {
        prompt_tokens: completion.usage?.prompt_tokens || 0,
        completion_tokens: completion.usage?.completion_tokens || 0,
        total_tokens: completion.usage?.total_tokens || 0
      }
    });

  } catch (error) {
    console.error('OpenAI API error:', error);

    // Fallback to pattern-based processing
    const fallbackResponse = processWithPatterns(req.body.message);

    res.json({
      success: true,
      response: fallbackResponse,
      fallback: true
    });
  }
});

// Fallback pattern-based processing
function processWithPatterns(input: string): AIResponse {
  const lowerInput = input.toLowerCase().trim();

  // Navigation patterns
  const navigationPatterns = [
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?profile/i, route: '/profile', name: 'profile' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:shopping\s+)?cart/i, route: '/cart', name: 'cart' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?orders/i, route: '/orders', name: 'orders' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:auto\s*shop|autoshop)/i, route: '/autoshop-dashboard', name: 'AutoShop dashboard' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:daswos\s+)?coins/i, route: '/daswos-coins', name: 'DasWos Coins' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?home(?:\s+page)?/i, route: '/', name: 'home' },
    { pattern: /(?:go to|navigate to|open|show me|take me to)\s+settings/i, route: '/settings', name: 'settings' },
    { pattern: /(?:sign in|log in|login|authenticate)/i, route: '/auth', name: 'sign in portal' },
  ];

  for (const { pattern, route, name } of navigationPatterns) {
    if (pattern.test(lowerInput)) {
      return {
        intent: 'navigation',
        action: 'navigate',
        parameters: { route },
        response: `Navigating to your ${name}...`,
        confidence: 0.9
      };
    }
  }

  // AutoShop patterns
  const autoShopPatterns = [
    /(?:start|enable|activate|begin|turn on)\s+(?:auto\s*shop|autoshop)/i,
    /(?:let's|lets)\s+(?:auto\s*shop|autoshop)/i,
    /(?:auto\s*shop|autoshop)\s+(?:start|begin|go|now)/i,
    /(?:start|begin)\s+(?:automatic\s+)?shopping/i,
    /(?:shop for me|shop automatically)/i,
  ];

  for (const pattern of autoShopPatterns) {
    if (pattern.test(lowerInput)) {
      return {
        intent: 'autoshop',
        action: 'start_autoshop',
        parameters: {},
        response: 'Starting AutoShop for you. I\'ll help you find and purchase items automatically!',
        confidence: 0.9
      };
    }
  }

  // Search patterns
  const searchPatterns = [
    /(?:search for|find|look for|show me|find me)\s+(.+)/i,
    /(?:search)\s+(.+)/i,
    /(?:i want to buy|i need|get me|buy me|purchase)\s+(.+)/i,
    /(?:can you find|help me find|locate)\s+(.+)/i,
    /(?:what about|how about|show me some)\s+(.+)/i,
    /(?:i'm looking for|looking for)\s+(.+)/i,
  ];

  for (const pattern of searchPatterns) {
    const match = lowerInput.match(pattern);
    if (match && match[1]) {
      const searchQuery = match[1].trim().replace(/\s+(please|now|today)$/i, '');
      return {
        intent: 'search',
        action: 'search',
        parameters: { query: searchQuery },
        response: `Searching for ${searchQuery}...`,
        confidence: 0.8
      };
    }
  }

  // Greeting patterns
  if (/^(hi|hello|hey|good morning|good afternoon|good evening)$/i.test(lowerInput)) {
    return {
      intent: 'conversation',
      response: 'Hello! I\'m DasWos AI, your intelligent shopping assistant. You can ask me to search for products, navigate the site, or start AutoShop. How can I help you today?',
      confidence: 0.9
    };
  }

  // Help patterns
  if (/^(help|what can you do|commands)$/i.test(lowerInput)) {
    return {
      intent: 'conversation',
      response: 'I can help you with:\n• Search for products: "Find me shoes"\n• Navigate: "Go to my profile", "Sign in"\n• Start AutoShop: "Let\'s AutoShop"\n• General questions about DasWos\n\nWhat would you like to do?',
      confidence: 0.9
    };
  }

  // Default response
  return {
    intent: 'conversation',
    response: 'I\'m not sure I understood that. You can ask me to search for products, navigate to different pages, or start AutoShop. Try saying something like "search for shoes" or "go to my profile".',
    confidence: 0.3
  };
}

// POST /api/ai-conversation/tts - Generate speech from text
router.post('/tts', async (req, res) => {
  try {
    const { text } = req.body;

    console.log('TTS request received:', { text });

    if (!text || typeof text !== 'string') {
      return res.status(400).json({ error: 'Text is required' });
    }

    if (text.length > 1000) {
      return res.status(400).json({ error: 'Text too long (max 1000 characters)' });
    }

    console.log('Generating speech with OpenAI TTS...');

    const speech = await openai.audio.speech.create({
      model: 'tts-1',
      voice: 'nova',
      input: text,
    });

    const audioBuffer = Buffer.from(await speech.arrayBuffer());
    console.log('Speech generated successfully, size:', audioBuffer.length);

    res.json({
      audio: audioBuffer.toString('base64')
    });

  } catch (error) {
    console.error('TTS error:', error);
    res.status(500).json({
      error: 'Failed to generate speech',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
