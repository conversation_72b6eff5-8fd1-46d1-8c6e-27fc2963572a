{"id": "173a0b03-1f14-473e-9cb4-369bf49e8ab5", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ai_shopper_recommendations": {"name": "ai_shopper_recommendations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "confidence": {"name": "confidence", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "purchased_at": {"name": "purchased_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejected_reason": {"name": "rejected_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.app_settings": {"name": "app_settings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"app_settings_key_unique": {"name": "app_settings_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bulk_buy_requests": {"name": "bulk_buy_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": false}, "request_type": {"name": "request_type", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": false}, "max_budget": {"name": "max_budget", "type": "integer", "primaryKey": false, "notNull": false}, "special_requirements": {"name": "special_requirements", "type": "text", "primaryKey": false, "notNull": false}, "preferred_delivery_date": {"name": "preferred_delivery_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'new'"}, "assigned_agent_id": {"name": "assigned_agent_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cart_items": {"name": "cart_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "added_at": {"name": "added_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": true, "default": "'manual'"}, "recommendation_id": {"name": "recommendation_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "level": {"name": "level", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.category_closure": {"name": "category_closure", "schema": "", "columns": {"ancestor_id": {"name": "ancestor_id", "type": "integer", "primaryKey": false, "notNull": true}, "descendant_id": {"name": "descendant_id", "type": "integer", "primaryKey": false, "notNull": true}, "depth": {"name": "depth", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"category_closure_ancestor_id_categories_id_fk": {"name": "category_closure_ancestor_id_categories_id_fk", "tableFrom": "category_closure", "tableTo": "categories", "columnsFrom": ["ancestor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "category_closure_descendant_id_categories_id_fk": {"name": "category_closure_descendant_id_categories_id_fk", "tableFrom": "category_closure", "tableTo": "categories", "columnsFrom": ["descendant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"category_closure_ancestor_id_descendant_id_pk": {"name": "category_closure_ancestor_id_descendant_id_pk", "columns": ["ancestor_id", "descendant_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collaborative_collaborators": {"name": "collaborative_collaborators", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "search_id": {"name": "search_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'collaborator'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collaborative_resources": {"name": "collaborative_resources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "search_id": {"name": "search_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "source_type": {"name": "source_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'website'"}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "requires_permission": {"name": "requires_permission", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collaborative_searches": {"name": "collaborative_searches", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "topic": {"name": "topic", "type": "text", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.daswos_coins_transactions": {"name": "daswos_coins_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'completed'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "related_order_id": {"name": "related_order_id", "type": "integer", "primaryKey": false, "notNull": false}, "related_split_buy_id": {"name": "related_split_buy_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.daswos_ai_chat_messages": {"name": "daswos_ai_chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.daswos_ai_chats": {"name": "daswos_ai_chats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "default": "'New Chat'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.daswos_ai_sources": {"name": "daswos_ai_sources", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "message_id": {"name": "message_id", "type": "integer", "primaryKey": false, "notNull": true}, "source_type": {"name": "source_type", "type": "text", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": false}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false}, "source_name": {"name": "source_name", "type": "text", "primaryKey": false, "notNull": true}, "relevance_score": {"name": "relevance_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.family_invitation_codes": {"name": "family_invitation_codes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "owner_user_id": {"name": "owner_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "used_by_user_id": {"name": "used_by_user_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"family_invitation_codes_owner_user_id_users_id_fk": {"name": "family_invitation_codes_owner_user_id_users_id_fk", "tableFrom": "family_invitation_codes", "tableTo": "users", "columnsFrom": ["owner_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "family_invitation_codes_used_by_user_id_users_id_fk": {"name": "family_invitation_codes_used_by_user_id_users_id_fk", "tableFrom": "family_invitation_codes", "tableTo": "users", "columnsFrom": ["used_by_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"family_invitation_codes_code_unique": {"name": "family_invitation_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.information_content": {"name": "information_content", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": true}, "source_name": {"name": "source_name", "type": "text", "primaryKey": false, "notNull": true}, "source_verified": {"name": "source_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "source_type": {"name": "source_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'website'"}, "trust_score": {"name": "trust_score", "type": "integer", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "verified_since": {"name": "verified_since", "type": "text", "primaryKey": false, "notNull": false}, "warning": {"name": "warning", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.order_items": {"name": "order_items", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order_id": {"name": "order_id", "type": "integer", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "price_at_purchase": {"name": "price_at_purchase", "type": "integer", "primaryKey": false, "notNull": true}, "item_name_snapshot": {"name": "item_name_snapshot", "type": "text", "primaryKey": false, "notNull": true}, "split_buy_id": {"name": "split_buy_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "order_date": {"name": "order_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "total_amount": {"name": "total_amount", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "shipping_address": {"name": "shipping_address", "type": "text", "primaryKey": false, "notNull": true}, "billing_address": {"name": "billing_address", "type": "text", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true}, "payment_reference": {"name": "payment_reference", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "seller_id": {"name": "seller_id", "type": "integer", "primaryKey": false, "notNull": true}, "seller_name": {"name": "seller_name", "type": "text", "primaryKey": false, "notNull": true}, "seller_verified": {"name": "seller_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "seller_type": {"name": "seller_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'merchant'"}, "trust_score": {"name": "trust_score", "type": "integer", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true}, "shipping": {"name": "shipping", "type": "text", "primaryKey": false, "notNull": true}, "original_price": {"name": "original_price", "type": "integer", "primaryKey": false, "notNull": false}, "discount": {"name": "discount", "type": "integer", "primaryKey": false, "notNull": false}, "verified_since": {"name": "verified_since", "type": "text", "primaryKey": false, "notNull": false}, "warning": {"name": "warning", "type": "text", "primaryKey": false, "notNull": false}, "is_bulk_buy": {"name": "is_bulk_buy", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "bulk_minimum_quantity": {"name": "bulk_minimum_quantity", "type": "integer", "primaryKey": false, "notNull": false}, "bulk_discount_rate": {"name": "bulk_discount_rate", "type": "integer", "primaryKey": false, "notNull": false}, "image_description": {"name": "image_description", "type": "text", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": false}, "ai_attributes": {"name": "ai_attributes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'"}, "search_vector": {"name": "search_vector", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"products_category_id_categories_id_fk": {"name": "products_category_id_categories_id_fk", "tableFrom": "products", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resource_permission_requests": {"name": "resource_permission_requests", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "resource_id": {"name": "resource_id", "type": "integer", "primaryKey": false, "notNull": true}, "requester_id": {"name": "requester_id", "type": "integer", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.search_queries": {"name": "search_queries", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "query": {"name": "query", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "sphere": {"name": "sphere", "type": "text", "primaryKey": false, "notNull": true}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'products'"}, "filters": {"name": "filters", "type": "jsonb", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "super_safe_enabled": {"name": "super_safe_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "super_safe_settings": {"name": "super_safe_settings", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.seller_verifications": {"name": "seller_verifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deposit_amount": {"name": "deposit_amount", "type": "integer", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "text", "primaryKey": false, "notNull": false}, "document_urls": {"name": "document_urls", "type": "text[]", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sellers": {"name": "sellers", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "business_name": {"name": "business_name", "type": "text", "primaryKey": false, "notNull": true}, "business_type": {"name": "business_type", "type": "text", "primaryKey": false, "notNull": true}, "verification_status": {"name": "verification_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "business_address": {"name": "business_address", "type": "text", "primaryKey": false, "notNull": true}, "contact_phone": {"name": "contact_phone", "type": "text", "primaryKey": false, "notNull": true}, "tax_id": {"name": "tax_id", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "year_established": {"name": "year_established", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "text", "primaryKey": false, "notNull": false}, "document_urls": {"name": "document_urls", "type": "text[]", "primaryKey": false, "notNull": false}, "trust_score": {"name": "trust_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sellers_user_id_unique": {"name": "sellers_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.split_buy_participants": {"name": "split_buy_participants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "split_buy_id": {"name": "split_buy_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "quantity_committed": {"name": "quantity_committed", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.split_buys": {"name": "split_buys", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true}, "initiator_user_id": {"name": "initiator_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "target_quantity": {"name": "target_quantity", "type": "integer", "primaryKey": false, "notNull": true}, "current_quantity": {"name": "current_quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "price_per_unit": {"name": "price_per_unit", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "min_participants": {"name": "min_participants", "type": "integer", "primaryKey": false, "notNull": false, "default": 2}, "max_participants": {"name": "max_participants", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_dasbar_preferences": {"name": "user_dasbar_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "items": {"name": "items", "type": "jsonb", "primaryKey": false, "notNull": true}, "max_visible_items": {"name": "max_visible_items", "type": "integer", "primaryKey": false, "notNull": true, "default": 4}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_dasbar_preferences_user_id_users_id_fk": {"name": "user_dasbar_preferences_user_id_users_id_fk", "tableFrom": "user_dasbar_preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_payment_methods": {"name": "user_payment_methods", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_payment_method_id": {"name": "stripe_payment_method_id", "type": "text", "primaryKey": false, "notNull": true}, "last4": {"name": "last4", "type": "text", "primaryKey": false, "notNull": true}, "card_type": {"name": "card_type", "type": "text", "primaryKey": false, "notNull": true}, "expiry_month": {"name": "expiry_month", "type": "integer", "primaryKey": false, "notNull": true}, "expiry_year": {"name": "expiry_year", "type": "integer", "primaryKey": false, "notNull": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "session_token": {"name": "session_token", "type": "text", "primaryKey": false, "notNull": true}, "device_info": {"name": "device_info", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_sessions_session_token_unique": {"name": "user_sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_seller": {"name": "is_seller", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_admin": {"name": "is_admin", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "has_subscription": {"name": "has_subscription", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "subscription_type": {"name": "subscription_type", "type": "text", "primaryKey": false, "notNull": false}, "subscription_expires_at": {"name": "subscription_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_family_owner": {"name": "is_family_owner", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "family_owner_id": {"name": "family_owner_id", "type": "integer", "primaryKey": false, "notNull": false}, "parent_account_id": {"name": "parent_account_id", "type": "integer", "primaryKey": false, "notNull": false}, "is_child_account": {"name": "is_child_account", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "super_safe_mode": {"name": "super_safe_mode", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "super_safe_settings": {"name": "super_safe_settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"blockGambling\":true,\"blockAdultContent\":true,\"blockOpenSphere\":false}'::jsonb"}, "safe_sphere_active": {"name": "safe_sphere_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "ai_shopper_enabled": {"name": "ai_shopper_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "ai_shopper_settings": {"name": "ai_shopper_settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"autoPurchase\":false,\"autoPaymentEnabled\":false,\"confidenceThreshold\":0.85,\"budgetLimit\":5000,\"maxTransactionLimit\":10000,\"preferredCategories\":[],\"avoidTags\":[],\"minimumTrustScore\":85,\"purchaseMode\":\"refined\",\"maxPricePerItem\":5000,\"maxCoinsPerItem\":50,\"maxCoinsPerDay\":100,\"maxCoinsOverall\":1000,\"purchaseFrequency\":{\"hourly\":1,\"daily\":5,\"monthly\":50}}'::jsonb"}, "daswos_coins": {"name": "daswos_coins", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}