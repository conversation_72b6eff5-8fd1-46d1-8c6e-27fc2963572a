import express from 'express';
import reviewsRoutes from './reviews';
import { setupAutoShopRoutes } from './autoshop';
import aiConversationRoutes from './ai-conversation';
import { storage } from '../storage';

const router = express.Router();

// Register all routes
router.use('/reviews', reviewsRoutes);
router.use('/ai-conversation', aiConversationRoutes);

// Setup AutoShop routes
setupAutoShopRoutes(router, storage);

export default router;
